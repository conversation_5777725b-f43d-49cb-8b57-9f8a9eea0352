import { faChevronUp } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cn } from "@utils/cn";
import { useCallback, useEffect, useRef, useState } from "react";

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps
  extends Omit<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    "size" | "value" | "onChange"
  > {
  className?: string;
  options?: SelectOption[];
  placeholder?: string;
  variant?: "primary" | "secondary" | "accent";
  size?: "sm" | "md" | "lg";
  value?: string;
  onChange?: (value: string) => void;
}

const SIZE_CLASSES: Record<NonNullable<SelectProps["size"]>, string> = {
  lg: "h-10 text-sm",
  md: "h-9 text-body-xs",
  sm: "h-8 text-xs",
};

const VARIANT_CLASSES: Record<NonNullable<SelectProps["variant"]>, string> = {
  accent: "select-accent",
  primary: "select-primary",
  secondary: "select-secondary",
};

export const Select = ({
  className,
  options = [
    { label: "Yes", value: "Yes" },
    { label: "No", value: "No" },
  ],
  placeholder = "Select an option",
  variant = "primary",
  size = "md",
  value,
  onChange,
  ...props
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = value
    ? options.find((option) => option.value === value)
    : null;

  const toggleDropdown = useCallback(() => {
    setIsOpen(!isOpen);
  }, [isOpen]);

  const handleOptionSelect = useCallback(
    (optionValue: string) => {
      onChange?.(optionValue);
      setIsOpen(false);
    },
    [onChange]
  );

  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     if (
  //       dropdownRef.current &&
  //       !dropdownRef.current.contains(event.target as Node)
  //     ) {
  //       setIsOpen(false);
  //     }
  //   };

  //   if (isOpen) {
  //     document.addEventListener("mousedown", handleClickOutside);
  //   }

  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, [isOpen]);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    },

    //   if (isOpen) {
    //     document.addEventListener("mousedown", handleClickOutside);
    //   }
    [isOpen]
  );

  return (
    <div ref={dropdownRef} className="relative w-full">
      <button
        type="button"
        onClick={toggleDropdown}
        className={cn(
          "flex w-full appearance-none items-center justify-between rounded-lg border-none bg-base-200 px-4",
          "focus:outline-none focus:ring-2 focus:ring-primary",
          SIZE_CLASSES[size],
          VARIANT_CLASSES[variant],
          className
        )}
        {...props}
      >
        {selectedOption?.label || placeholder}
        <FontAwesomeIcon
          icon={faChevronUp}
          className={cn(
            "select-icon",
            { "rotate-180": isOpen },
            "transition-transform duration-200"
          )}
        />
      </button>
      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-1 w-full rounded-lg border border-base-300 bg-base-100 p-2 shadow-lg">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleOptionSelect(option.value)}
              className={cn(
                "w-full rounded-lg px-4 py-2 text-left hover:bg-primary/10",
                {
                  "bg-primary/20": value === option.value,
                }
              )}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
